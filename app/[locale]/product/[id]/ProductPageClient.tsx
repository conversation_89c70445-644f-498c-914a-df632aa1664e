'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import { addToCart as addToSessionCart } from '../../../../lib/session-cart';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';

interface ProductPageClientProps {
  initialProduct: ProductWithDetails | null;
  initialCategory: Category | null;
  initialSubcategory: Subcategory | null;
  locale: Locale;
  productId: string;
}

export default function ProductPageClient({
  initialProduct,
  initialCategory,
  initialSubcategory,
  locale,
  productId
}: ProductPageClientProps) {
  const router = useRouter();
  const [product, setProduct] = useState<ProductWithDetails | null>(initialProduct);
  const [category, setCategory] = useState<Category | null>(initialCategory);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [activeTab, setActiveTab] = useState('description');
  const [whatsappSettings] = useState({
    businessNumber: '+966 599252259',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  const fetchProductDetails = useCallback(async (productId: string) => {
    try {
      setLoading(true);
      console.log('🚀 Client: Fetching product details for ID:', productId);

      // إنشاء AbortController للتحكم في timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية

      // جلب جميع البيانات في طلب واحد محسن
      const response = await fetch(`/api/products/${productId}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('📡 Client: Product response status:', response.status);

      if (!response.ok) {
        console.error('❌ Client: Product API response not ok:', response.status);
        if (response.status === 404) {
          router.push(`/${locale}/products`);
          return;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📦 Client: Product API response:', result);

      if (result.success && result.data) {
        const { product, category, subcategory } = result.data;

        setProduct(product);
        if (category) setCategory(category);
        if (subcategory) setSubcategory(subcategory);
      } else {
        console.error('❌ Client: Invalid product data structure:', result);
        router.push(`/${locale}/products`);
      }
    } catch (error) {
      console.error('❌ Client: Error fetching product details:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('❌ Client: Request timeout');
      }
      // في حالة الخطأ، توجيه المستخدم إلى صفحة المنتجات
      router.push(`/${locale}/products`);
    } finally {
      setLoading(false);
    }
  }, [router, locale]);

  // تحديث البيانات عند تغيير البيانات الأولية أو جلبها من العميل
  useEffect(() => {
    if (initialProduct) {
      // استخدام البيانات الأولية إذا كانت متوفرة
      setProduct(initialProduct);
      setCategory(initialCategory || null);
      setSubcategory(initialSubcategory || null);
      setLoading(false);
    } else if (productId) {
      // جلب البيانات من API إذا لم تكن البيانات الأولية متوفرة
      fetchProductDetails(productId);
    }
  }, [productId, initialProduct, initialCategory, initialSubcategory, fetchProductDetails]);

  const addToCart = (qty?: number) => {
    if (!product) return;

    const quantityToAdd = qty || quantity;
    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const cartItem = {
      id: product.id,
      title: productTitle,
      titleAr: product.title_ar,
      price: product.price,
      quantity: quantityToAdd,
      image: product.images && product.images.length > 0 ? product.images[0].image_url : '/placeholder-image.jpg'
    };

    addToSessionCart(cartItem);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const handleAddToCartClick = () => {
    addToCart();
  };

  const sendWhatsAppMessage = () => {
    if (!product || !whatsappSettings.enabled) return;

    // استخدام الرابط المختصر الجديد
    const whatsappUrl = `/${locale}/whatsapp/${product.id}`;
    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">{t('productDetails.loading')}</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('productDetails.notFound')}</h2>
          <p className="text-gray-600 mb-8">{t('productDetails.notFoundMessage')}</p>
          <button
            onClick={() => router.push(`/${locale}/products`)}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
          >
            {t('productDetails.backToProducts')}
          </button>
        </div>
      </div>
    );
  }



  // Responsive view with enhanced mobile design
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header with Back Button */}
      <div className="lg:hidden sticky top-0 z-40 bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <button
            onClick={() => router.back()}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <i className={`ri-arrow-${locale === 'ar' ? 'right' : 'left'}-line text-lg text-gray-700`}></i>
          </button>
          <h2 className="text-lg font-semibold text-gray-900 truncate mx-4 flex-1 text-center">
            {locale === 'ar' ? product.title_ar : product.title}
          </h2>
          <div className="w-10"></div> {/* Spacer for centering */}
        </div>
      </div>

      {/* Desktop Breadcrumb - Hidden on Mobile */}
      <div className="hidden lg:block container mx-auto px-4 py-8">
        <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 mb-8">
          <a href={`/${locale}`} className="hover:text-primary transition-colors">
            {t('home')}
          </a>
          <span className="text-gray-400">/</span>
          <a href={`/${locale}/products`} className="hover:text-primary transition-colors">
            {t('products')}
          </a>
          {category && (
            <>
              <span className="text-gray-400">/</span>
              <span className="text-gray-500">{category.name}</span>
            </>
          )}
          {subcategory && (
            <>
              <span className="text-gray-400">/</span>
              <span className="text-gray-500">{subcategory.name}</span>
            </>
          )}
          <span className="text-gray-400">/</span>
          <span className="text-primary font-medium">{locale === 'ar' ? product.title_ar : product.title}</span>
        </nav>
      </div>

      <div className="lg:max-w-4xl lg:mx-auto lg:px-4 lg:pb-8">
        <div className="lg:grid lg:grid-cols-2 lg:gap-6">
          {/* Product Images - Enhanced Mobile Design */}
          <div className="lg:space-y-3">
            {/* Main Image - Full width on mobile */}
            <div className="relative group">
              <div className="aspect-square lg:aspect-[4/3] bg-white lg:rounded-2xl overflow-hidden lg:shadow-lg lg:border lg:border-gray-100 lg:max-w-sm lg:mx-auto">
                {product.images && product.images.length > 0 ? (
                  <Image
                    src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                    alt={locale === 'ar' ? product.title_ar : product.title}
                    width={500}
                    height={500}
                    className="w-full h-full object-cover lg:group-hover:scale-105 transition-transform duration-300"
                    priority
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400 bg-gray-100">
                    <div className="text-center">
                      <i className="ri-image-line text-6xl lg:text-4xl mb-2 lg:mb-1"></i>
                      <p className="text-base lg:text-sm">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Enhanced Image Counter Badge */}
              {product.images && product.images.length > 1 && (
                <div className="absolute top-4 right-4 lg:top-3 lg:right-3 bg-black/70 text-white px-3 py-2 lg:px-2 lg:py-1 rounded-xl lg:rounded-lg text-sm lg:text-xs font-medium backdrop-blur-sm">
                  {selectedImageIndex + 1} / {product.images.length}
                </div>
              )}

              {/* Mobile Navigation Arrows */}
              {product.images && product.images.length > 1 && (
                <>
                  <button
                    onClick={() => setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : product.images!.length - 1)}
                    className="lg:hidden absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-gray-700 hover:bg-white transition-all"
                  >
                    <i className={`ri-arrow-${locale === 'ar' ? 'right' : 'left'}-s-line text-xl`}></i>
                  </button>
                  <button
                    onClick={() => setSelectedImageIndex(selectedImageIndex < product.images!.length - 1 ? selectedImageIndex + 1 : 0)}
                    className="lg:hidden absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-gray-700 hover:bg-white transition-all"
                  >
                    <i className={`ri-arrow-${locale === 'ar' ? 'left' : 'right'}-s-line text-xl`}></i>
                  </button>
                </>
              )}
            </div>

            {/* Enhanced Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className="px-4 lg:px-0 py-4 lg:py-0">
                <div className="flex justify-center lg:justify-center space-x-3 lg:space-x-2 rtl:space-x-reverse overflow-x-auto pb-2 lg:pb-0">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 lg:w-12 lg:h-12 rounded-xl lg:rounded-lg overflow-hidden border-2 transition-all ${
                        selectedImageIndex === index
                          ? 'border-primary shadow-lg scale-110 lg:scale-100 lg:shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:scale-105 lg:hover:scale-100'
                      }`}
                    >
                      <Image
                        src={image.image_url}
                        alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Product Details - Enhanced Mobile Design */}
          <div className="bg-white lg:bg-transparent lg:space-y-4">
            {/* Mobile Product Info Card */}
            <div className="lg:hidden px-4 py-6 border-b border-gray-100">
              {/* Availability Badge - Mobile */}
              <div className="flex justify-center mb-4">
                <div className={`px-4 py-2 rounded-full text-sm font-semibold flex items-center space-x-2 rtl:space-x-reverse ${
                  product.is_available
                    ? 'bg-green-100 text-green-800 border border-green-200'
                    : 'bg-red-100 text-red-800 border border-red-200'
                }`}>
                  <div className={`w-3 h-3 rounded-full ${
                    product.is_available ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span>{product.is_available ? t('available') : t('unavailable')}</span>
                </div>
              </div>

              {/* Product Meta Info */}
              <div className="flex items-center justify-center space-x-6 rtl:space-x-reverse text-sm text-gray-500 mb-4">
                <span className="flex items-center space-x-1 rtl:space-x-reverse">
                  <i className="ri-barcode-line text-primary"></i>
                  <span>{locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}</span>
                </span>
                {category && (
                  <span className="flex items-center space-x-1 rtl:space-x-reverse">
                    <i className="ri-folder-line text-primary"></i>
                    <span>{locale === 'ar' ? category.name_ar : category.name}</span>
                  </span>
                )}
              </div>
            </div>

            {/* Desktop Header Section */}
            <div className="hidden lg:block">
              <div className="flex items-start justify-between mb-2">
                <h1 className="text-xl lg:text-2xl font-bold text-gray-900 leading-tight">
                  {locale === 'ar' ? product.title_ar : product.title}
                  {locale === 'ar' && ' - أدوات بوفيه عالية الجودة'}
                  {locale === 'en' && ' - Premium Buffet Equipment'}
                </h1>

                {/* Availability Badge - Desktop */}
                <div className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1 rtl:space-x-reverse ${
                  product.is_available
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    product.is_available ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span>{product.is_available ? t('available') : t('unavailable')}</span>
                </div>
              </div>

              <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
                <span className="flex items-center space-x-1 rtl:space-x-reverse">
                  <i className="ri-barcode-line"></i>
                  <span>{locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}</span>
                </span>
                {category && (
                  <span className="flex items-center space-x-1 rtl:space-x-reverse">
                    <i className="ri-folder-line"></i>
                    <span>{locale === 'ar' ? category.name_ar : category.name}</span>
                  </span>
                )}
              </div>
            </div>

            {/* Enhanced Price Section */}
            <div className="px-4 lg:px-0 py-6 lg:py-0 border-b lg:border-b-0 border-gray-100">
              <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl lg:rounded-xl p-6 lg:p-4 shadow-sm lg:shadow-none">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm lg:text-xs font-semibold lg:font-medium text-gray-600 uppercase tracking-wide mb-2 lg:mb-1">
                      {locale === 'ar' ? 'السعر' : 'Price'}
                    </p>
                    <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
                      <span className="text-4xl lg:text-3xl font-bold text-primary">
                        {product.price}
                      </span>
                      <span className="text-xl lg:text-lg text-primary/70 font-semibold lg:font-medium">
                        {t('currency')}
                      </span>
                    </div>
                  </div>

                  <div className="w-16 h-16 lg:w-12 lg:h-12 bg-primary/10 rounded-2xl lg:rounded-xl flex items-center justify-center">
                    <i className="ri-price-tag-3-line text-2xl lg:text-xl text-primary"></i>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Content Tabs */}
            <div className="px-4 lg:px-0 py-6 lg:py-0 lg:space-y-3">
              {/* Enhanced Tab Navigation */}
              <div className="flex space-x-1 rtl:space-x-reverse bg-gray-100 p-1 rounded-2xl lg:rounded-xl shadow-sm lg:shadow-none">
                {[
                  { key: 'description', label: t('productDetails.description'), icon: 'ri-file-text-line' },
                  { key: 'features', label: t('productDetails.features'), icon: 'ri-star-line' },
                  { key: 'specs', label: t('productDetails.technicalSpecs'), icon: 'ri-settings-3-line' }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key)}
                    className={`flex-1 flex items-center justify-center space-x-1 rtl:space-x-reverse px-3 py-3 lg:px-2 lg:py-2 rounded-xl lg:rounded-lg font-semibold lg:font-medium transition-all text-sm ${
                      activeTab === tab.key
                        ? 'bg-white text-primary shadow-md lg:shadow-sm scale-105 lg:scale-100'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                    }`}
                  >
                    <i className={`${tab.icon} text-lg lg:text-base`}></i>
                    <span className="hidden sm:inline lg:inline">{tab.label}</span>
                  </button>
                ))}
              </div>

              {/* Enhanced Tab Content */}
              <div className="min-h-[150px] lg:min-h-[120px] mt-4 lg:mt-3">
                {/* Description Tab */}
                {activeTab === 'description' && (product.description || product.description_ar) && (
                  <div className="bg-gray-50 rounded-2xl lg:rounded-xl p-6 lg:p-4 shadow-sm lg:shadow-none">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-line text-base lg:text-sm">
                      {locale === 'ar' ? product.description_ar : product.description}
                    </p>
                  </div>
                )}

                {/* Features Tab */}
                {activeTab === 'features' && product.features && product.features.length > 0 && (
                  <div className="bg-gray-50 rounded-2xl lg:rounded-xl p-6 lg:p-4 shadow-sm lg:shadow-none">
                    <div className="space-y-3 lg:space-y-2">
                      {product.features.map((feature, index) => (
                        <div key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                          <div className="flex-shrink-0 w-6 h-6 lg:w-5 lg:h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                            <i className="ri-check-line text-white text-sm lg:text-xs"></i>
                          </div>
                          <span className="text-gray-700 text-base lg:text-sm leading-relaxed">
                            {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Specifications Tab */}
                {activeTab === 'specs' && product.specifications && product.specifications.length > 0 && (
                  <div className="bg-gray-50 rounded-2xl lg:rounded-xl p-6 lg:p-4 shadow-sm lg:shadow-none">
                    <div className="space-y-3 lg:space-y-2">
                      {product.specifications.map((spec, index) => (
                        <div key={index} className="flex items-center justify-between py-3 lg:py-2 border-b border-gray-200 last:border-b-0">
                          <span className="font-semibold lg:font-medium text-gray-800 text-base lg:text-sm">
                            {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}
                          </span>
                          <span className="text-gray-600 text-base lg:text-sm">
                            {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced Quantity and Actions */}
            <div className="px-4 lg:px-0 py-6 lg:py-0 lg:space-y-4">
              {/* Enhanced Quantity Selector */}
              <div className="flex items-center justify-between mb-6 lg:mb-4">
                <label className="text-base lg:text-sm font-semibold lg:font-medium text-gray-700">
                  {t('productDetails.quantity')}
                </label>
                <div className="flex items-center bg-gray-100 rounded-2xl lg:rounded-lg shadow-sm lg:shadow-none">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-12 h-12 lg:w-8 lg:h-8 flex items-center justify-center text-gray-600 hover:text-primary transition-colors rounded-l-2xl lg:rounded-l-lg"
                  >
                    <i className="ri-subtract-line text-lg lg:text-base"></i>
                  </button>
                  <div className="w-16 h-12 lg:w-12 lg:h-8 flex items-center justify-center border-x border-gray-200 bg-white">
                    <span className="text-lg lg:text-sm font-bold lg:font-medium text-gray-800">{quantity}</span>
                  </div>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-12 h-12 lg:w-8 lg:h-8 flex items-center justify-center text-gray-600 hover:text-primary transition-colors rounded-r-2xl lg:rounded-r-lg"
                  >
                    <i className="ri-add-line text-lg lg:text-base"></i>
                  </button>
                </div>
              </div>

              {/* Enhanced Action Buttons */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 lg:gap-2">
                <button
                  onClick={handleAddToCartClick}
                  disabled={!product.is_available}
                  className="bg-primary text-white py-3 lg:py-2 px-4 lg:px-3 rounded-xl lg:rounded-lg font-semibold lg:font-medium hover:bg-primary-dark transition-all disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 lg:space-x-1 rtl:space-x-reverse shadow-md lg:shadow-sm hover:shadow-lg lg:hover:shadow-md transform hover:scale-105 lg:hover:scale-100"
                >
                  <i className="ri-shopping-cart-line text-xl lg:text-base"></i>
                  <span className="text-lg lg:text-base">{t('productDetails.addToCart')}</span>
                </button>

                <button
                  onClick={sendWhatsAppMessage}
                  className="bg-green-500 text-white py-4 lg:py-3 px-6 lg:px-4 rounded-2xl lg:rounded-xl font-bold lg:font-semibold hover:bg-green-600 transition-all flex items-center justify-center space-x-3 lg:space-x-2 rtl:space-x-reverse shadow-lg lg:shadow-none hover:shadow-xl lg:hover:shadow-sm transform hover:scale-105 lg:hover:scale-100"
                >
                  <i className="ri-whatsapp-line text-xl lg:text-base"></i>
                  <span className="text-lg lg:text-base">{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Toast Notification */}
      {showToast && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 lg:bottom-6 lg:right-6 lg:left-auto lg:transform-none bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-4 rounded-2xl shadow-2xl z-50 animate-fade-in border border-green-400 max-w-sm lg:max-w-none">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 lg:w-8 lg:h-8 bg-white/20 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-xl lg:text-lg font-bold"></i>
            </div>
            <span className="font-bold lg:font-semibold text-lg">{t('cartMessages.addedToCart')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
